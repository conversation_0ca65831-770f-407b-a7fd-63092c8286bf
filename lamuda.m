% 设置时间范围和步长
t = 0:0.1:50;  % 从0到50秒，步长0.1秒

% 创建三条曲线
y1 = zeros(size(t));
y2 = zeros(size(t));
y3 = zeros(size(t));

% 锯齿参数
initial_amplitude = 0.1;   % 初始锯齿幅度
initial_period = 3;        % 初始周期（3秒一个波动）
frequency_growth = 0.05;   % 频率增长率（较慢的增长）
amplitude_decay = 0.02;    % 幅度衰减率

% 计算曲线1
for i = 1:length(t)
    if t(i) <= 12
        y1(i) = -45 + (-25 - (-45)) * t(i)/12;  % 从-45到-25的线性变化
    else
        % 计算随时间变化的参数
        time_since_12 = t(i) - 12;

        % 频率逐渐增加（周期逐渐减小）
        current_frequency = 1/initial_period + frequency_growth * time_since_12;

        % 幅度逐渐减小
        current_amplitude = initial_amplitude * exp(-amplitude_decay * time_since_12);

        % 生成三角波（波峰在周期中间）
        phase = current_frequency * time_since_12;
        normalized_phase = mod(phase, 1);  % 0到1之间的相位

        % 创建三角波：前半周期上升，后半周期下降，波峰在0.5处
        triangle_value = 1 - 4 * abs(normalized_phase - 0.5);  % 三角波公式

        sawtooth_offset = current_amplitude * triangle_value;

        y1(i) = -25 + sawtooth_offset;  % 保持-25并添加锯齿
    end
end

% 计算曲线2
for i = 1:length(t)
    if t(i) <= 12
        y2(i) = -12 + (-15 - (-12)) * t(i)/12;  % 从-12到-15的线性变化
    else
        % 计算随时间变化的参数
        time_since_12 = t(i) - 12;

        % 频率逐渐增加
        current_frequency = 1/initial_period + frequency_growth * time_since_12;

        % 幅度逐渐减小
        current_amplitude = initial_amplitude * exp(-amplitude_decay * time_since_12);

        % 生成三角波（波峰在周期中间）
        phase = current_frequency * time_since_12;
        normalized_phase = mod(phase, 1);  % 0到1之间的相位

        % 创建三角波：前半周期上升，后半周期下降，波峰在0.5处
        triangle_value = 1 - 4 * abs(normalized_phase - 0.5);  % 三角波公式

        sawtooth_offset = current_amplitude * triangle_value;

        y2(i) = -15 + sawtooth_offset;  % 保持-15并添加锯齿
    end
end

% 计算曲线3
for i = 1:length(t)
    if t(i) <= 12
        y3(i) = 6 + (30 - 6) * t(i)/12;  % 从6到30的线性变化
    else
        % 计算随时间变化的参数
        time_since_12 = t(i) - 12;

        % 频率逐渐增加
        current_frequency = 1/initial_period + frequency_growth * time_since_12;

        % 幅度逐渐减小
        current_amplitude = initial_amplitude * exp(-amplitude_decay * time_since_12);

        % 生成三角波（波峰在周期中间）
        phase = current_frequency * time_since_12;
        normalized_phase = mod(phase, 1);  % 0到1之间的相位

        % 创建三角波：前半周期上升，后半周期下降，波峰在0.5处
        triangle_value = 1 - 4 * abs(normalized_phase - 0.5);  % 三角波公式

        sawtooth_offset = current_amplitude * triangle_value;

        y3(i) = 30 + sawtooth_offset;  % 保持30并添加锯齿
    end
end

% 绘制图形
figure;
plot(t, y1, 'Color', [0.8 0.6 0], 'LineWidth', 2);  % 深黄色曲线1
hold on;
plot(t, y2, 'Color', [0.5 0.8 1], 'LineWidth', 2);  % 浅蓝色曲线2
plot(t, y3, 'Color', [0.8 0 0], 'LineWidth', 2);  % 深红色曲线3


% 设置图形属性
title('  ');
xlabel('时间 (s)');
ylabel('λ (°)');
legend('导弹1', '导弹2', '导弹3', 'Location', 'northwest');
grid on;

% 设置坐标轴范围
xlim([0, 50]);  % 横坐标范围0-50秒
ylim([-50, 30]); % 纵坐标范围-50到30度

% 设置更详细的坐标轴刻度
xticks(0:5:50);  % 横坐标每5秒一个刻度
yticks(-50:10:30);  % 纵坐标每5度一个刻度

% 提高图像质量
set(gcf, 'Color', 'w');  % 白色背景
set(gca, 'FontSize', 15);  % 增大字体
% box on;  % 添加边框