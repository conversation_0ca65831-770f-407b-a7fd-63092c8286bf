% 设置时间范围和步长
t = 0:0.1:50;  % 从0到50秒，步长0.1秒

% 创建三条曲线
y1 = zeros(size(t));
y2 = zeros(size(t));
y3 = zeros(size(t));

% 锯齿参数
sawtooth_amplitude = 0.5;  % 锯齿幅度，增大以使锯齿更明显
initial_frequency = 2;     % 初始频率（12秒时的频率）
frequency_growth = 0.5;    % 频率增长率

% 计算曲线1
for i = 1:length(t)
    if t(i) <= 12
        y1(i) = -45 + (-25 - (-45)) * t(i)/12;  % 从-45到-25的线性变化
    else
        % 计算随时间增加的频率
        time_since_12 = t(i) - 12;
        current_frequency = initial_frequency + frequency_growth * time_since_12;

        % 生成连续的锯齿波，使用三角波以确保连续性
        phase = 2 * pi * current_frequency * time_since_12;
        sawtooth_offset = sawtooth_amplitude * (2/pi) * asin(sin(phase));

        y1(i) = -25 + sawtooth_offset;  % 保持-25并添加锯齿
    end
end

% 计算曲线2
for i = 1:length(t)
    if t(i) <= 12
        y2(i) = -12 + (-15 - (-12)) * t(i)/12;  % 从-12到-15的线性变化
    else
        % 计算随时间增加的频率
        time_since_12 = t(i) - 12;
        current_frequency = initial_frequency + frequency_growth * time_since_12;

        % 生成连续的锯齿波
        phase = 2 * pi * current_frequency * time_since_12;
        sawtooth_offset = sawtooth_amplitude * (2/pi) * asin(sin(phase));

        y2(i) = -15 + sawtooth_offset;  % 保持-15并添加锯齿
    end
end

% 计算曲线3
for i = 1:length(t)
    if t(i) <= 12
        y3(i) = 6 + (30 - 6) * t(i)/12;  % 从6到30的线性变化
    else
        % 计算随时间增加的频率
        time_since_12 = t(i) - 12;
        current_frequency = initial_frequency + frequency_growth * time_since_12;

        % 生成连续的锯齿波
        phase = 2 * pi * current_frequency * time_since_12;
        sawtooth_offset = sawtooth_amplitude * (2/pi) * asin(sin(phase));

        y3(i) = 30 + sawtooth_offset;  % 保持30并添加锯齿
    end
end

% 绘制图形
figure;
plot(t, y1, 'b', 'LineWidth', 2);  % 蓝色曲线1
hold on;
plot(t, y2, 'r', 'LineWidth', 2);  % 红色虚线曲线2
plot(t, y3, 'g', 'LineWidth', 2);  % 绿色点划线曲线3


% 设置图形属性
title('  ');
xlabel('时间 (s)');
ylabel('λ (°)');
legend('导弹1', '导弹2', '导弹3', 'Location', 'northwest');
grid on;

% 设置坐标轴范围
xlim([0, 50]);  % 横坐标范围0-50秒
ylim([-50, 30]); % 纵坐标范围-50到30度


% 提高图像质量
set(gcf, 'Color', 'w');  % 白色背景
set(gca, 'FontSize', 15);  % 增大字体
% box on;  % 添加边框